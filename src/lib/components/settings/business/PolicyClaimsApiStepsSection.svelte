<script lang="ts">
	import { Timeline, TimelineItem, Badge, Card } from 'flowbite-svelte';
	import { 
		CheckCircleSolid, 
		ClockSolid, 
		UserSolid, 
		FileLinesSolid,
		CogSolid,
		DatabaseSolid,
		ServerSolid
	} from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';

	// Step data based on the documentation
	const apiSteps = [
		{
			id: 1,
			title: 'Get Authentication Token',
			description: 'Retrieve authentication token from TPA API',
			icon: ServerSolid,
			endpoint: 'POST https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/GetToken',
			requestBody: {
				"USERNAME": "BVTPA",
				"PASSWORD": "*d!n^+Cb@1",
				"SOCIAL_ID": "Value from platform_user_id in customer_customerplatformidentity table",
				"CHANNEL_ID": "Value from channel_id in customer_customerplatformidentity table",
				"CHANNEL": "LINE"
			},
			responseExample: '"112233445566"',
			badgeColor: 'blue' as const
		},
		{
			id: 2,
			title: 'Verify Citizen ID',
			description: 'Validate Citizen ID with external API',
			icon: CheckCircleSolid,
			endpoint: 'POST https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/SearchCitizenID',
			requestBody: {
				"SOCIAL_ID": "Value from platform_user_id",
				"CHANNEL_ID": "Value from channel_id",
				"CHANNEL": "LINE"
			},
			responseExample: {
				"ListOfSearchCitizenID": [
					{
						"Status": "1",
						"CitizenID": "2019086318637"
					}
				],
				"ErrorMessage": " "
			},
			badgeColor: 'purple' as const
		},
		{
			id: 3,
			title: 'Fetch Policy List',
			description: 'Retrieve customer policy list using verified Citizen ID',
			icon: FileLinesSolid,
			endpoint: 'POST https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyListSocial',
			requestBody: {
				"SOCIAL_ID": "Value from platform_user_id",
				"CHANNEL_ID": "Value from channel_id",
				"CHANNEL": "LINE",
				"CITIZEN_ID": "Value from CitizenID in step 4 response"
			},
			responseExample: {
				"ListOfPolicyListSocial": [
					{
						"Name": "ทดสอบ2",
						"CitizenID": "2019086318637",
						"PolNo": "BVTPA_2024",
						"MemberCode": "202400006-2 Chatbot4",
						"EffFrom": "15/02/2024",
						"EffTo": "20/12/2025"
					}
				]
			},
			badgeColor: 'indigo' as const
		},
		// {
		// 	id: ,
		// 	title: 'Extract MemberCodes',
		// 	description: 'Extract and save MemberCode from each policy to temporary list',
		// 	icon: CogSolid,
		// 	endpoint: 'Backend Processing',
		// 	requestBody: null,
		// 	responseExample: 'List of MemberCodes extracted for further processing',
		// 	badgeColor: 'pink' as const
		// },
		{
			id: 4,
			title: 'Fetch Policy & Claims Details',
			description: 'Get detailed policy and claims data for each MemberCode',
			icon: DatabaseSolid,
			endpoint: 'POST https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyDetailSocial',
			requestBody: {
				"SOCIAL_ID": "Value from platform_user_id",
				"CHANNEL_ID": "Value from channel_id",
				"CHANNEL": "LINE",
				"MEMBER_CODE": "Value from MemberCode in step 6"
			},
			responseExample: {
				"ListOfPolDet": "Policy details with coverage information",
				"ListOfPolClaim": "Claims data with status and amounts"
			},
			badgeColor: 'red' as const
		}
	];
</script>

<div class="space-y-6 rounded-lg bg-white p-6 shadow-md">
	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-xl font-medium text-gray-700">Policy & Claims API Integration Steps</h2>
			<p class="text-sm text-gray-500">Step-by-step process for fetching insurance policies and claims data from external APIs</p>
		</div>
	</div>

	<div class="space-y-4">
		<Timeline>
			{#each apiSteps as step}
				<TimelineItem title={step.title} date="Step {step.id}">
					<svelte:component this={step.icon} slot="icon" class="w-4 h-4 text-{step.badgeColor}-600" />

					<Card class="mt-3" size="xl">
						<div class="space-y-4">
							<p class="text-sm text-gray-600">{step.description}</p>

							{#if step.endpoint && step.endpoint !== 'Backend Cache Storage' && step.endpoint !== 'Database Query' && step.endpoint !== 'Backend Processing'}
								<div>
									<Badge color={step.badgeColor} class="mb-2">API Endpoint</Badge>
									<code class="block bg-gray-100 p-2 rounded text-xs break-all">{step.endpoint}</code>
								</div>
							{:else if step.endpoint}
								<div>
									<Badge color={step.badgeColor} class="mb-2">Process</Badge>
									<code class="block bg-gray-100 p-2 rounded text-xs">{step.endpoint}</code>
								</div>
							{/if}

							{#if step.requestBody}
								<div>
									<Badge color="dark" class="mb-2">Request Body</Badge>
									<pre class="bg-gray-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(step.requestBody, null, 2)}</code></pre>
								</div>
							{/if}

							{#if step.responseExample}
								<div>
									<Badge color="green" class="mb-2">Response Example</Badge>
									{#if typeof step.responseExample === 'string'}
										<code class="block bg-green-50 p-2 rounded text-xs">{step.responseExample}</code>
									{:else}
										<pre class="bg-green-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(step.responseExample, null, 2)}</code></pre>
									{/if}
								</div>
							{/if}
						</div>
					</Card>
				</TimelineItem>
			{/each}
		</Timeline>
	</div>

	<div class="mt-6 p-4 bg-blue-50 border-l-4 border-blue-400 rounded">
		<div class="flex">
			<div class="flex-shrink-0">
				<svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
					<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
				</svg>
			</div>
			<div class="ml-3">
				<h3 class="text-sm font-medium text-blue-800">Important Notes</h3>
				<div class="mt-2 text-sm text-blue-700">
					<ul class="list-disc list-inside space-y-1">
						<li>Authentication token has 1-hour expiry and can be reused for multiple API calls</li>
						<li>Customer must have a valid Citizen ID to proceed with policy fetching</li>
						<li>Each policy contains a unique MemberCode used for detailed data retrieval</li>
						<li>Policy and claims data are stored in CustomerPolicies table with PolNo as composite primary key</li>
						<li>System checks for existing data and updates only if changes are detected</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>
